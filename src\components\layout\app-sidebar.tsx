// src/components/layout/app-sidebar.tsx
'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  route: string;
  subItems?: { id: string; label: string; route: string }[];
}

interface AppSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const AppSidebar = ({ isCollapsed, onToggleCollapse }: AppSidebarProps) => {
  const [expandedItems, setExpandedItems] = useState<string[]>(['Student Management']);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [activeSubSection, setActiveSubSection] = useState<string>('Current Students');
  const router = useRouter();
  const pathname = usePathname();

  const sidebarItems: SidebarItem[] = [
    {
      id: 'Dashboard',
      label: 'Dashboard',
      route: '/dashboard',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
        </svg>
      ),
    },
    {
      id: 'Student Management',
      label: 'Student Management',
      route: '/student-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      subItems: [
        { id: 'Current Students', label: 'Current Students', route: '/student-management' },
        { id: 'Enroll Student', label: 'Enroll Student', route: '/student-management' },
        { id: 'Class Assignment', label: 'Class Assignment', route: '/student-management' },
        { id: 'School Records', label: 'School Records', route: '/student-management' },
      ],
    },
    {
      id: 'Staff Management',
      label: 'Staff Management',
      route: '/staff-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
    {
      id: 'Academic Management',
      label: 'Academic Management',
      route: '/academic-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
    },
    {
      id: 'Attendance Management',
      label: 'Attendance Management',
      route: '/attendance-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
    },
    {
      id: 'Fee Management',
      label: 'Fee Management',
      route: '/fee-management',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      id: 'Reports',
      label: 'Reports',
      route: '/reports',
      icon: (
        <svg className={isCollapsed ? "sidebar-icon-collapsed" : "sidebar-icon-expanded"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
    },
  ];

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleItemClick = (item: SidebarItem) => {
    if (item.subItems) {
      toggleExpanded(item.id);
    } else {
      router.push(item.route);
    }
  };

  const handleSubItemClick = (subItem: { id: string; label: string; route: string }) => {
    setActiveSubSection(subItem.id);
    router.push(subItem.route);
  };

  const isActiveRoute = (route: string) => {
    return pathname === route || pathname.startsWith(route + '/');
  };

  // Fixed Issue #2: Sub-navigation selection logic
  const isActiveSubItem = (subItem: { id: string; label: string; route: string }) => {
    // For student management, check if we're on the route AND if it's the current active section
    if (pathname === '/student-management') {
      return subItem.id === activeSubSection;
    }
    return pathname === subItem.route;
  };

  return (
    <div className={`sidebar-dark h-full flex flex-col border-r transition-all duration-300 relative ${
      isCollapsed ? 'w-16' : 'w-72'
    }`} style={{ overflow: 'visible' }}>
      {/* Modern Collapse Toggle Arrow - Positioned in middle of sidebar */}
      <div 
        className={`sidebar-collapse-toggle ${isCollapsed ? 'collapsed' : ''}`}
        onClick={onToggleCollapse}
        style={{ top: '50%' }}
      >
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
        </svg>
      </div>

      {/* Sidebar Header */}
      <div className="sidebar-header sidebar-header-height flex items-center justify-center px-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-400 rounded-xl flex items-center justify-center shadow-lg shadow-blue-400/40">
              <span className="text-white font-bold text-base-app">EP</span>
            </div>
            <span className="sidebar-brand text-lg-app">EduPro</span>
          </div>
        )}
        {isCollapsed && (
          <div className="flex items-center justify-center w-full">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-400 rounded-lg flex items-center justify-center shadow-lg shadow-blue-400/40">
              <span className="text-white font-bold text-sm-app">EP</span>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Items */}
      <nav className={`flex-1 mt-6 ${isCollapsed ? 'px-0' : 'px-0'} flex flex-col items-center`}>
        {sidebarItems.map((item) => (
          <div key={item.id} className={`mb-3 ${isCollapsed ? 'w-full flex justify-center' : 'w-full'}`}>
            {isCollapsed ? (
              // Collapsed state with tooltip - perfectly centered
              <div
                className={`sidebar-item-collapsed sidebar-nav-item ${
                  isActiveRoute(item.route) ? 'active' : ''
                }`}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
                onClick={() => handleItemClick(item)}
              >
                {item.icon}
                <div className={`sidebar-tooltip ${hoveredItem === item.id ? 'show' : ''}`}>
                  {item.label}
                </div>
              </div>
            ) : (
              // Expanded state - centered with proper margins
              <div className="px-3">
                <button
                  onClick={() => handleItemClick(item)}
                  className={`sidebar-nav-item w-full flex items-center justify-between px-4 py-2.5 rounded-lg text-left transition-colors ${
                    isActiveRoute(item.route) ? 'active' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {item.icon}
                    <span className="sidebar-label text-sm">{item.label}</span>
                  </div>
                  {item.subItems && (
                    <svg
                      className={`w-4 h-4 transition-transform ${
                        expandedItems.includes(item.id) ? 'rotate-90' : ''
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  )}
                </button>
              </div>
            )}

            {/* Sub Items - only show when expanded and not collapsed */}
            {!isCollapsed && item.subItems && expandedItems.includes(item.id) && (
              <div className="ml-8 mt-1 space-y-1 px-3">
                {item.subItems.map((subItem) => (
                  <button
                    key={subItem.id}
                    onClick={() => handleSubItemClick(subItem)}
                    className={`sidebar-subitem w-full text-left px-3 py-2 rounded-lg sidebar-sublabel transition-colors text-sm ${
                      isActiveSubItem(subItem) ? 'active' : ''
                    }`}
                  >
                    {subItem.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>
    </div>
  );
};

export default AppSidebar;
